package kr.wayplus.qr_hallimpark.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 문제 검색 필드 정의
 * - 검색 가능한 필드들을 체계적으로 관리
 * - 필드 추가/수정이 매우 간단함
 */
@Getter
@RequiredArgsConstructor
public enum QuizSearchField {

    // 프론트엔드에서 사용 가능한 검색 필드들
    ALL("all", null, "전체", true),
    TITLE("title", "qm.title", "문제 제목", true),
    QUIZ_TYPE("quiz_type", "qm.quiz_type", "문제 유형", true),
    QUESTION("question", "qc.question", "문제 내용", false),
    CATEGORY_NAME("category_name", "cat.category_name", "카테고리명", false),
    DIFFICULTY_LEVEL("difficulty_level", "qm.difficulty_level", "난이도", false),
    CREATE_ID("create_id", "qm.create_id", "생성자", false),
    HINT("hint", "qm.hint", "힌트", false),
    CORRECT_ANSWER("correct_answer", "qm.correct_answer", "정답", false),
    PARENT_CATEGORY_NAME("parent_category_name", "parent_cat.category_name", "상위 카테고리명", false),
    STATUS("status", "qm.status", "상태", false),
    IMAGE_URL("image_url", "qm.image_url", "이미지 URL", false),
    CREATE_DATE("create_date", "qm.create_date", "생성일", false),
    LAST_UPDATE_DATE("last_update_date", "qm.last_update_date", "수정일", false);

    private final String fieldName;      //검색필드명
    private final String dbColumn;      // 데이터베이스 컬럼명
    private final String description;   
    private final boolean isFront;       // 프론트엔드에서 사용 가능 여부
    
    /**
     * 필드 키로 검색 필드 찾기
     * @param fieldName 필드 키
     * @return 검색 필드 (없으면 null)
     */
    public static QuizSearchField findByFieldName(String fieldName) {
        if (fieldName == null) {
            return ALL;
        }
        
        for (QuizSearchField field : values()) {
            if (field.fieldName.equals(fieldName)) {
                return field;
            }
        }
        return null;
    }
    
    /**
     * 유효한 검색 필드인지 확인
     * @param fieldName 필드 키
     * @return 유효 여부
     */
    public static boolean isValidField(String fieldName) {
        return findByFieldName(fieldName) != null;
    }
    
    /**
     * 전체 검색 필드인지 확인
     * @return 전체 검색 여부
     */
    public boolean isAllSearch() {
        return this == ALL;
    }
    
    /**
     * 프론트엔드용 허용된 검색 필드 Map 반환 (한국어 설명: 필드키)
     * @return 허용된 필드 Map (description: fieldName)
     */
    public static java.util.Map<String, String> getAllowedFieldNames() {
        java.util.Map<String, String> fieldMap = new java.util.LinkedHashMap<>();
        java.util.Arrays.stream(values())
                .filter(field -> field.isFront)
                .forEach(field -> fieldMap.put(field.getDescription(), field.getFieldName()));
        return fieldMap;
    }
}
