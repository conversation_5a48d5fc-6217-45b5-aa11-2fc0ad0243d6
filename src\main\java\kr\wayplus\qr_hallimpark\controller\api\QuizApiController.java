package kr.wayplus.qr_hallimpark.controller.api;

import kr.wayplus.qr_hallimpark.enums.QuizSearchField;
import kr.wayplus.qr_hallimpark.enums.QuizSortField;
import kr.wayplus.qr_hallimpark.model.Quiz;
import kr.wayplus.qr_hallimpark.model.external.QuizApiResponse;
import kr.wayplus.qr_hallimpark.mapper.QuizMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Quiz API 컨트롤러
 * - 문제 관련 API
 * - CORS 설정으로 외부 도메인 접근 허용
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://192.168.0.227:9998", allowCredentials = "true")
public class QuizApiController {

    private final QuizMapper quizMapper;

    /**
     * 문제 목록 조회 (검색, 정렬, 페이징 기능 포함)
     * @param page 페이지 번호 (기본값: 1)
     * @param size 페이지 크기 (기본값: 20, 최대: 100)
     * @param categoryId 카테고리 ID (선택사항)
     * @param searchKeyword 검색 키워드 (선택사항)
     * @param selectedSearchField 검색 필드 (title: 문제 제목, quiz_type: 문제 유형, all: 전체)
     * @param sortField 정렬 필드 (기본값: quiz_id)
     * @param sortDirection 정렬 방향 (ASC: 오름차순, DESC: 내림차순, 기본값: DESC)
     * @return 문제 목록 응답
     */
    @GetMapping("/quizzes")
    public HashMap<String, Object> getQuizzes(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size,
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @RequestParam(value = "selectedSearchField", defaultValue = "all") String selectedSearchField,
            @RequestParam(value = "sortField", defaultValue = "quiz_id") String sortField,
            @RequestParam(value = "sortDirection", defaultValue = "DESC") String sortDirection) {

        log.debug("page: {}, size: {}, categoryId: {}, searchKeyword: {}, selectedSearchField: {}, sortField: {}, sortDirection: {}",
                  page, size, categoryId, searchKeyword, selectedSearchField, sortField, sortDirection);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 페이지 및 크기 유효성 검사
            if (page < 1) {
                page = 1;
            }
            if (size < 1) {
                size = 20;
            }
            size = Math.min(size, 100); // 최대 100개로 제한

            // 검색 필드 유효성 검사 및 매핑
            String mappedSearchField = validateAndMapSearchField(selectedSearchField);

            // 정렬 필드 유효성 검사 및 매핑
            String mappedSortField = validateAndMapSortField(sortField);

            // 정렬 방향 유효성 검사
            String validSortDirection = validateSortDirection(sortDirection);

            // offset 계산 (음수 방지)
            int offset = Math.max(0, (page - 1) * size);

            // 검색 파라미터 구성
            Map<String, Object> params = new HashMap<>();
            params.put("categoryId", categoryId);
            params.put("searchKeyword", searchKeyword);
            params.put("selectedSearchField", selectedSearchField);
            params.put("mappedSearchField", mappedSearchField); // 매핑된 검색 필드
            params.put("sortField", sortField);
            params.put("mappedSortField", mappedSortField); // 매핑된 정렬 필드
            params.put("sortDirection", validSortDirection);
            params.put("offset", offset);
            params.put("size", size);

            log.debug("Query parameters - page: {}, size: {}, offset: {}, categoryId: {}, searchKeyword: {}, selectedSearchField: {}, mappedSearchField: {}, sortField: {}, mappedSortField: {}, sortDirection: {}",
                      page, size, offset, categoryId, searchKeyword, selectedSearchField, mappedSearchField, sortField, mappedSortField, validSortDirection);

            // 데이터 조회
            List<Quiz> quizzes = quizMapper.selectActiveQuizzesForApi(params);
            Long totalCount = quizMapper.countActiveQuizzesForApi(params);

            // 응답 형식으로 변환
            List<QuizApiResponse> apiQuizzes = quizzes.stream()
                    .map(QuizApiResponse::fromQuiz)
                    .collect(Collectors.toList());

            // 페이징 정보 계산
            int totalPages = (int) Math.ceil((double) totalCount / size);
            boolean hasNext = page < totalPages;
            boolean hasPrevious = page > 1;

            // 응답 데이터 구성
            HashMap<String, Object> data = new HashMap<>();
            data.put("items", apiQuizzes);
            data.put("currentPage", page);
            data.put("totalPages", totalPages);
            data.put("totalCount", totalCount);
            data.put("hasNext", hasNext);
            data.put("hasPrevious", hasPrevious);
            data.put("pageSize", size);

            // 허용된 검색 필드 키 제공 (모든 페이지에 포함)
            data.put("allowedSearchFields", QuizSearchField.getAllowedFieldNames());

            // 허용된 정렬 필드 키 제공 (모든 페이지에 포함)
            data.put("allowedSortFields", QuizSortField.getAllowedSortFields());

            // 허용된 페이지 크기 옵션 제공
            data.put("allowedPageSizes", Arrays.asList(10, 20, 50, 99));

            // 정렬 방향 옵션 제공
            Map<String, String> sortDirections = new LinkedHashMap<>();
            sortDirections.put("내림차순", "DESC");
            sortDirections.put("오름차순", "ASC");
            data.put("allowedSortDirections", sortDirections);

            response.put("success", true);
            response.put("data", data);

            log.debug("Successfully retrieved {} quizzes out of {} total", apiQuizzes.size(), totalCount);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid request parameters: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "INVALID_PARAMETERS");

        } catch (Exception e) {
            log.error("Error retrieving quizzes : {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 목록 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 단일 문제 조회
     * @param quizId 문제 ID
     * @return 문제 정보
     */
    @GetMapping("/quizzes/{quizId}")
    public HashMap<String, Object> getQuiz(@PathVariable("quizId") Long quizId) {
        log.debug("Getting quiz by ID: {}", quizId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            Quiz quiz = quizMapper.selectQuizById(quizId);

            if (quiz == null) {
                response.put("success", false);
                response.put("message", "문제를 찾을 수 없습니다.");
                response.put("errorCode", "QUIZ_NOT_FOUND");
                return response;
            }

            // 활성화된 문제만 반환
            if (!"ACTIVE".equals(quiz.getStatus())) {
                response.put("success", false);
                response.put("message", "비활성화된 문제입니다.");
                response.put("errorCode", "QUIZ_INACTIVE");
                return response;
            }

            QuizApiResponse apiQuiz = QuizApiResponse.fromQuiz(quiz);

            response.put("success", true);
            response.put("data", apiQuiz);

            log.debug("Successfully retrieved quiz: {}", quizId);

        } catch (Exception e) {
            log.error("Error retrieving quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 검색 필드 유효성 검사 및 매핑 (Enum 사용)
     * @param selectedSearchField 선택된 검색 필드
     * @return 매핑된 데이터베이스 필드명
     */
    private String validateAndMapSearchField(String selectedSearchField) {
        QuizSearchField searchField = QuizSearchField.findByFieldName(selectedSearchField);

        if (searchField == null) {
            log.warn("Invalid search field: {}", selectedSearchField);
            throw new IllegalArgumentException("유효하지 않은 검색 필드입니다: " + selectedSearchField);
        }

        if (searchField.isAllSearch()) {
            return null; // 전체 검색인 경우
        }

        return searchField.getDbColumn();
    }

    /**
     * 정렬 필드 유효성 검사 및 매핑 (Enum 사용)
     * @param sortField 선택된 정렬 필드
     * @return 매핑된 데이터베이스 필드명
     */
    private String validateAndMapSortField(String sortField) {
        QuizSortField field = QuizSortField.findByFieldKey(sortField);

        if (field == null) {
            log.warn("Invalid sort field: {}, using default", sortField);
            return QuizSortField.QUIZ_ID.getDbColumn(); // 기본값
        }

        return field.getDbColumn();
    }

    /**
     * 정렬 방향 유효성 검사
     * @param sortDirection 정렬 방향
     * @return 유효한 정렬 방향 (ASC 또는 DESC)
     */
    private String validateSortDirection(String sortDirection) {
        if (sortDirection == null) {
            return "DESC";
        }

        String upperDirection = sortDirection.toUpperCase();
        if ("ASC".equals(upperDirection) || "DESC".equals(upperDirection)) {
            return upperDirection;
        }

        log.warn("Invalid sort direction: {}, using default DESC", sortDirection);
        return "DESC";
    }
}
