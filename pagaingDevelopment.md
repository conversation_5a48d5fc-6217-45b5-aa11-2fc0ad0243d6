🚀 검색 기능 개발 방법론
1단계: Enum 기반 검색 필드 정의
각 도메인별로 검색 필드 Enum을 생성합니다.

// 예시: UserSearchField.java
@Getter
@RequiredArgsConstructor
public enum UserSearchField {
    
    // 프론트엔드에서 사용 가능한 검색 필드들
    ALL("all", null, "전체", true),
    USER_NAME("user_name", "u.user_name", "사용자명", true),
    EMAIL("email", "u.user_email", "이메일", true),
    PHONE("phone", "u.user_mobile", "전화번호", true),
    DEPARTMENT("department", "u.department", "부서", true),
    
    // 관리자용 추가 필드들
    USER_ID("user_id", "u.user_id", "사용자ID", false),
    CREATE_DATE("create_date", "u.user_join_date", "가입일", false),
    LAST_LOGIN("last_login", "u.last_login_date", "최종로그인", false);
    
    private final String fieldKey;      // API에서 사용하는 키
    private final String dbColumn;      // 데이터베이스 컬럼명  
    private final String description;   // 한국어 설명
    private final boolean isPublic;     // 프론트엔드에 노출 여부
    
    // 필수 메서드들
    public static UserSearchField findByFieldKey(String fieldKey) {
        if (fieldKey == null) return ALL;
        for (UserSearchField field : values()) {
            if (field.fieldKey.equals(fieldKey)) return field;
        }
        return null;
    }
    
    public boolean isAllSearch() {
        return this == ALL;
    }
    
    public static Map<String, String> getAllowedFieldKeys() {
        Map<String, String> fieldMap = new LinkedHashMap<>();
        Arrays.stream(values())
                .filter(field -> field.isPublic)
                .forEach(field -> fieldMap.put(field.getDescription(), field.getFieldKey()));
        return fieldMap;
    }
}

2단계: Mapper 인터페이스에 전용 메서드 추가
// UserMapper.java
@Mapper
@Repository
public interface UserMapper {
    
    // 기존 메서드들...
    
    // ========== API 전용 메서드 ==========
    
    /**
     * API용 사용자 목록 조회 (검색 기능 포함)
     * @param params 검색 파라미터
     * @return 사용자 목록
     */
    List<User> selectUsersForApi(Map<String, Object> params);
    
    /**
     * API용 사용자 총 개수 조회 (검색 조건 적용)
     * @param params 검색 파라미터
     * @return 총 개수
     */
    Long countUsersForApi(Map<String, Object> params);
}
3단계: XML에서 검색 조건 정의
<!-- user-mapper.xml -->

<!-- 검색 조건 (API용) -->
<sql id="apiSearchConditions">
    <if test="searchKeyword != null and searchKeyword.trim() != ''">
        <choose>
            <when test="selectedSearchField == 'all' or mappedSearchField == null">
                <!-- 전체 검색 - 주요 필드들에서 검색 -->
                AND (u.user_name LIKE CONCAT('%', #{searchKeyword}, '%') 
                     OR u.user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                     OR u.user_mobile LIKE CONCAT('%', #{searchKeyword}, '%')
                     OR u.department LIKE CONCAT('%', #{searchKeyword}, '%'))
            </when>
            <otherwise>
                <!-- 단일 필드 검색 - Java에서 매핑된 필드 사용 -->
                AND ${mappedSearchField} LIKE CONCAT('%', #{searchKeyword}, '%')
            </otherwise>
        </choose>
    </if>
</sql>

<!-- 정렬 조건 (API용) -->
<sql id="apiOrderBy">
    ORDER BY u.user_join_date DESC
</sql>

<!-- API용 사용자 목록 조회 -->
<select id="selectUsersForApi" parameterType="map" resultMap="UserResultMap">
    SELECT 
        u.user_id,
        u.user_name,
        u.user_email,
        u.user_mobile,
        u.department,
        u.user_join_date,
        u.last_login_date
    FROM users u
    WHERE u.delete_yn = 'N'
    
    <!-- 부서 필터 -->
    <if test="department != null">
        AND u.department = #{department}
    </if>
    
    <!-- 검색 조건 적용 -->
    <include refid="apiSearchConditions"/>
    
    <!-- 정렬 적용 -->
    <include refid="apiOrderBy"/>
    
    <!-- 페이징 -->
    <if test="offset != null and size != null">
        LIMIT #{size} OFFSET #{offset}
    </if>
</select>

<!-- API용 사용자 총 개수 조회 -->
<select id="countUsersForApi" parameterType="map" resultType="long">
    SELECT COUNT(*)
    FROM users u
    WHERE u.delete_yn = 'N'
    
    <!-- 부서 필터 -->
    <if test="department != null">
        AND u.department = #{department}
    </if>
    
    <!-- 검색 조건 적용 -->
    <include refid="apiSearchConditions"/>
</select>
4단계: Controller 구현
// UserApiController.java
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class UserApiController {

    private final UserMapper userMapper;

    /**
     * 사용자 목록 조회 (검색 기능 포함)
     */
    @GetMapping("/users")
    public HashMap<String, Object> getUsers(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size,
            @RequestParam(value = "department", required = false) String department,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @RequestParam(value = "selectedSearchField", defaultValue = "all") String selectedSearchField) {

        log.debug("page: {}, size: {}, department: {}, searchKeyword: {}, selectedSearchField: {}",
                  page, size, department, searchKeyword, selectedSearchField);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 페이지 및 크기 유효성 검사
            if (page < 1) page = 1;
            if (size < 1) size = 20;
            size = Math.min(size, 100);

            // 검색 필드 유효성 검사 및 매핑
            String mappedSearchField = validateAndMapSearchField(selectedSearchField);

            // offset 계산 (음수 방지)
            int offset = Math.max(0, (page - 1) * size);

            // 검색 파라미터 구성
            Map<String, Object> params = new HashMap<>();
            params.put("department", department);
            params.put("searchKeyword", searchKeyword);
            params.put("selectedSearchField", selectedSearchField);
            params.put("mappedSearchField", mappedSearchField);
            params.put("offset", offset);
            params.put("size", size);

            // 데이터 조회
            List<User> users = userMapper.selectUsersForApi(params);
            Long totalCount = userMapper.countUsersForApi(params);

            // 페이징 정보 계산
            int totalPages = (int) Math.ceil((double) totalCount / size);
            boolean hasNext = page < totalPages;
            boolean hasPrevious = page > 1;

            // 응답 데이터 구성
            HashMap<String, Object> data = new HashMap<>();
            data.put("items", users);
            data.put("currentPage", page);
            data.put("totalPages", totalPages);
            data.put("totalCount", totalCount);
            data.put("hasNext", hasNext);
            data.put("hasPrevious", hasPrevious);
            data.put("pageSize", size);
            
            // 허용된 검색 필드 키 제공 (모든 페이지에 포함)
            data.put("allowedSearchFields", UserSearchField.getAllowedFieldKeys());

            response.put("success", true);
            response.put("data", data);

        } catch (Exception e) {
            log.error("Error retrieving users: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "사용자 목록 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 검색 필드 유효성 검사 및 매핑
     */
    private String validateAndMapSearchField(String selectedSearchField) {
        UserSearchField searchField = UserSearchField.findByFieldKey(selectedSearchField);
        
        if (searchField == null) {
            log.warn("Invalid search field: {}", selectedSearchField);
            throw new IllegalArgumentException("유효하지 않은 검색 필드입니다: " + selectedSearchField);
        }
        
        if (searchField.isAllSearch()) {
            return null; // 전체 검색인 경우
        }
        
        return searchField.getDbColumn();
    }
}
🎯 핵심 개발 패턴
패턴 1: 3-Layer 구조
Enum: 검색 필드 정의 및 매핑
XML: 동적 검색 조건 처리
Controller: 유효성 검사 및 응답 구성
패턴 2: 확장 가능한 설계
새 검색 필드 추가: Enum에 한 줄만 추가
복잡한 검색 조건: XML에 <if> 조건 추가
다양한 필터: params Map에 추가
패턴 3: 일관된 API 응답
{
  "success": true,
  "data": {
    "items": [...],
    "currentPage": 1,
    "totalPages": 5,
    "totalCount": 100,
    "hasNext": true,
    "hasPrevious": false,
    "pageSize": 20,
    "allowedSearchFields": {
      "전체": "all",
      "사용자명": "user_name",
      "이메일": "email"
    }
  }
}
🔧 다른 도메인 적용 예시
카테고리 검색 (CategorySearchField)
CATEGORY_NAME("category_name", "c.category_name", "카테고리명", true),
PARENT_CATEGORY("parent_category", "pc.category_name", "상위카테고리", true),
DESCRIPTION("description", "c.description", "설명", true),
STATUS("status", "c.status", "상태", false);
주문 검색 (OrderSearchField)
ORDER_NUMBER("order_number", "o.order_number", "주문번호", true),
CUSTOMER_NAME("customer_name", "c.customer_name", "고객명", true),
PRODUCT_NAME("product_name", "p.product_name", "상품명", true),
ORDER_STATUS("order_status", "o.order_status", "주문상태", true);
📋 체크리스트
새로운 검색 기능 개발 시 확인사항:

Enum 생성: 도메인별 SearchField Enum 작성
필수 메서드: findByFieldKey(), isAllSearch(), getAllowedFieldKeys() 구현
Mapper 메서드: select***ForApi(), count***ForApi() 추가
XML 조건: apiSearchConditions, apiOrderBy SQL fragment 작성
Controller: 유효성 검사, 매핑, 페이징 로직 구현
에러 처리: 음수 offset 방지, 잘못된 필드 검증
로깅: 디버그 로그로 파라미터 추적