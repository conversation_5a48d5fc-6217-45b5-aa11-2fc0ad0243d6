package kr.wayplus.qr_hallimpark.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 문제 정렬 필드 정의
 * - 정렬 가능한 필드들을 체계적으로 관리
 */
@Getter
@RequiredArgsConstructor
public enum QuizSortField {
    
    // 정렬 가능한 필드들
    QUIZ_ID("quiz_id", "qm.quiz_id", "등록", false),
    TITLE("title", "qm.title", "문제 제목", true),
    QUIZ_TYPE("quiz_type", "qm.quiz_type", "문제 유형", true),
    CATEGORY_NAME("category_name", "cat.category_name", "카테고리명", false),
    DIFFICULTY_LEVEL("difficulty_level", "qm.difficulty_level", "난이도", false),
    CREATE_DATE("create_date", "qm.create_date", "생성일", true),
    LAST_UPDATE_DATE("last_update_date", "qm.last_update_date", "수정일", true),
    
    // 추가 정렬 필드 (필요시 계속 추가 가능)
    STATUS("status", "qm.status", "상태순", false);
    
    private final String fieldKey;      // API에서 사용하는 키
    private final String dbColumn;      // 데이터베이스 컬럼명
    private final String description;   // 한국어 설명
    private final boolean isPublic;     // 프론트엔드에 노출 여부
    
    /**
     * 필드 키로 정렬 필드 찾기
     * @param fieldKey 필드 키
     * @return 정렬 필드 (없으면 null)
     */
    public static QuizSortField findByFieldKey(String fieldKey) {
        if (fieldKey == null) {
            return TITLE; // 기본값
        }
        
        for (QuizSortField field : values()) {
            if (field.fieldKey.equals(fieldKey)) {
                return field;
            }
        }
        return TITLE; // 기본값
    }
    
    /**
     * 유효한 정렬 필드인지 확인
     * @param fieldKey 필드 키
     * @return 유효 여부
     */
    public static boolean isValidField(String fieldKey) {
        return findByFieldKey(fieldKey) != null;
    }
    
    /**
     * 프론트엔드용 허용된 정렬 필드 Map 반환 (한국어 설명: 필드키)
     * @return 허용된 정렬 필드 Map (description: fieldKey)
     */
    public static java.util.Map<String, String> getAllowedSortFields() {
        java.util.Map<String, String> fieldMap = new java.util.LinkedHashMap<>();
        java.util.Arrays.stream(values())
                .filter(field -> field.isPublic)
                .forEach(field -> fieldMap.put(field.getDescription(), field.getFieldKey()));
        return fieldMap;
    }
}
